from langchain_openai import ChatOpenAI

# 初始化LLM
llm = ChatOpenAI(
    api_key="lm-studio",
    base_url="http://localhost:2000/v1",
    model="qwen/qwen3-30b-a3b-2507",
    temperature=0.7
)

def process_user_input(user_input):
    """
    接收用户输入的主题，并以AI的角度来解析和复述。
    
    Args:
        user_input (str): 用户输入的主题。
    
    Returns:
        str: AI解析和复述的结果。
    """
    # 构造提示词
    prompt = f"请以AI的角度解析并复述以下主题：\n\n{user_input}\n\n请以'用户希望...'的格式直接回答，不需要详细解释。"
    
    # 调用LLM处理提示词
    response = llm.invoke(prompt)
    
    # 返回结果
    return response.content

if __name__ == "__main__":
    # 示例调用
    user_input = input("请输入您的主题: ")
    result = process_user_input(user_input)
    print(result)
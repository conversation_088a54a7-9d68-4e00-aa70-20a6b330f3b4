<!DOCTYPE html>
<html>
<head>
    <title>不止AI Agent</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f0f0f0;
            height: 100vh;
            overflow: hidden;
        }
        .chat-container {
            width: 100%;
            height: 100vh;
            background-color: white;
            border-radius: 0;
            box-shadow: none;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        .chat-header {
            background-color: #4a90e2;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            flex-shrink: 0;
        }
        .chat-messages {
            flex-grow: 1;
            overflow-y: auto;
            padding: 20px;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 5px;
        }
        .user-message {
            background-color: #e3f2fd;
            text-align: right;
        }
        .agent-message {
            background-color: #f5f5f5;
        }
        .agent-message.step1 {
            background-color: #e8f5e9;
        }
        .agent-message.step2 {
            background-color: #fff3e0;
        }
        .chat-input {
            display: flex;
            padding: 20px;
            background-color: #f5f5f5;
            flex-shrink: 0;
        }
        .chat-input input {
            flex-grow: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-right: 10px;
        }
        .chat-input button {
            padding: 10px 20px;
            background-color: #4a90e2;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .typing-indicator {
            display: none;
            color: #999;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">不止AI</div>
        <div class="chat-messages" id="chat-messages"></div>
        <div class="typing-indicator" id="typing-indicator">正在输入...</div>
        <div class="chat-input">
            <input type="text" id="user-input" placeholder="请输入您的问题">
            <button id="send-btn">发送</button>
        </div>
    </div>

    <script>
        const chatMessages = document.getElementById('chat-messages');
        const userInput = document.getElementById('user-input');
        const sendBtn = document.getElementById('send-btn');
        const typingIndicator = document.getElementById('typing-indicator');

        function addMessage(content, isUser) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user-message' : 'agent-message'}`;
            messageDiv.textContent = content;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function addStepMessage(content, stepClass) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message agent-message ${stepClass}`;
            messageDiv.textContent = content;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function typeMessage(content, messageDiv) {
            let index = 0;
            messageDiv.textContent = '';
            typingIndicator.style.display = 'block';
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;

            function type() {
                if (index < content.length) {
                    messageDiv.textContent += content.charAt(index);
                    index++;
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                    setTimeout(type, 20);
                } else {
                    typingIndicator.style.display = 'none';
                }
            }

            type();
        }

        sendBtn.addEventListener('click', async () => {
            const message = userInput.value.trim();
            if (message) {
                addMessage(message, true);
                userInput.value = '';

                // 使用fetch API替代EventSource
                try {
                    const response = await fetch(`http://localhost:8003/chat?user_input=${encodeURIComponent(message)}`);
                    
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    
                    if (!response.body) {
                        throw new Error('ReadableStream not supported in this browser.');
                    }
                    
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder('utf-8');
                    
                    let step1MessageDiv = null;
                    let step2MessageDiv = null;
                    let step1Content = '';
                    let step2Content = '';
                    let isStep2 = false;
                    
                    while (true) {
                        const { done, value } = await reader.read();
                        
                        if (done) {
                            break;
                        }
                        
                        const chunk = decoder.decode(value, { stream: true });
                        
                        // 解析chunk中的事件
                        const lines = chunk.split('\n\n');
                        
                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                const data = line.substring(6); // 移除 'data: ' 前缀
                                
                                // 尝试解析JSON数据
                                let content;
                                try {
                                    const parsedData = JSON.parse(data);
                                    content = parsedData.content;
                                } catch (e) {
                                    // 如果解析失败，直接使用data
                                    content = data;
                                }
                                
                                // 检查是否是换行符，表示开始第二步输出
                                if (content === "\n") {
                                    isStep2 = true;
                                    // 创建第二步消息div
                                    step2MessageDiv = document.createElement('div');
                                    step2MessageDiv.className = 'message agent-message step2';
                                    // 使用打字机效果显示第二步消息
                                    typeMessage('', step2MessageDiv);
                                    chatMessages.scrollTop = chatMessages.scrollHeight;
                                    continue;
                                }
                                
                                // 如果还没有创建第一步消息div，创建它
                                if (!step1MessageDiv) {
                                    step1MessageDiv = document.createElement('div');
                                    step1MessageDiv.className = 'message agent-message step1';
                                    chatMessages.appendChild(step1MessageDiv);
                                    chatMessages.scrollTop = chatMessages.scrollHeight;
                                }
                                
                                // 根据当前步骤添加内容
                                if (isStep2) {
                                    step2Content += content;
                                    step2MessageDiv.textContent = step2Content;
                                    chatMessages.scrollTop = chatMessages.scrollHeight;
                                } else {
                                    step1Content += content;
                                    step1MessageDiv.textContent = step1Content;
                                    chatMessages.scrollTop = chatMessages.scrollHeight;
                                }
                            }
                        }
                    }
                } catch (error) {
                    console.error('Error:', error);
                }
            }
        });

        userInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendBtn.click();
            }
        });
    </script>
</body>
</html>
# 不止AI Agent

from langchain_openai import ChatOpenAI
from langchain_core.tools import tool
from langgraph.prebuilt import create_react_agent
from langchain_core.messages import HumanMessage, AIMessage
import logging

# 引入注册表
from registry import registry

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 初始化LLM
llm = ChatOpenAI(
    api_key="lm-studio",
    base_url="http://localhost:2000/v1",
    model="qwen/qwen3-30b-a3b-2507",
    temperature=0.7
)

# 定义Agent名称和角色设定
agent_name = "不止AI"
role_setting = "你是一个通用型智能助手。"

# 维护对话历史
conversation_history = []

# 主函数，用于处理用户输入
def process_user_input(user_input):
    # 将用户输入添加到对话历史
    conversation_history.append(HumanMessage(content=user_input))
    
    # 第一步：调用prompt_assistant获取复述结果
    prompt_assistant_process = registry.get("prompt_assistant")
    restated_input = prompt_assistant_process(user_input)
    
    # 第二步：以复述结果作为新输入执行并返回结果
    result = llm.invoke([HumanMessage(content=restated_input)])
    conversation_history.append(AIMessage(content=result.content))
    return result.content

# Web函数，用于处理用户输入
async def web_process_user_input(user_input):
    logger.info(f"web_process_user_input called with user_input: {user_input}")
    # 将用户输入添加到对话历史
    conversation_history.append(HumanMessage(content=user_input))
    
    # 第一步：调用prompt_assistant获取复述结果
    logger.info("Calling prompt_assistant_process")
    prompt_assistant_process = registry.get("prompt_assistant")
    restated_input = prompt_assistant_process(user_input)
    logger.info(f"Restated input: {restated_input}")
    
    # 先返回复述结果
    yield f" {restated_input}"
    
    # 发送一个换行符
    yield "\n"
    
    # 第二步：以复述结果作为新输入执行并返回结果
    logger.info("Calling llm.invoke")
    # 使用流式方式调用LLM
    response_stream = llm.stream([HumanMessage(content=restated_input)])
    
    # 逐段返回LLM的输出并收集所有内容
    final_result = ""
    for chunk in response_stream:
        if chunk.content:
            yield chunk.content
            final_result += chunk.content
    
    # 将最终结果添加到对话历史
    conversation_history.append(AIMessage(content=final_result))

# 示例调用
if __name__ == "__main__":
    while True:
        user_input = input("请输入您的问题 (输入'退出'以结束对话): ")
        if user_input.lower() == '退出':
            break
        result = process_user_input(user_input)
        print(f"结果: {result}")
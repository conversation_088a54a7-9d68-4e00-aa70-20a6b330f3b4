from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.responses import HTMLResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.middleware.cors import CORSMiddleware
import asyncio
import json
import logging
from buzhi_ai_agent import web_process_user_input

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

templates = Jinja2Templates(directory="templates")
app = FastAPI()

# 添加CORS中间件以允许跨域请求
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:8000", "http://localhost:8002", "http://localhost:8003"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/", response_class=HTMLResponse)
async def get(request: Request):
    return templates.TemplateResponse("chat.html", {"request": request})

@app.get('/chat')
async def chat(request: Request):
    user_input = request.query_params.get('user_input', '')
    logger.info(f"Received user input: {user_input}")
    if not user_input:
        logger.error("No user input provided")
        return StreamingResponse(iter(["data: {\"content\": \"No user input provided\"}\n\n"]), media_type="text/event-stream")
    
    async def event_generator():
        try:
            # 调用web_process_user_input并逐段发送输出
            logger.info("Calling web_process_user_input")
            async for chunk in web_process_user_input(user_input):
                logger.info(f"Sending chunk: {chunk}")
                yield f"data: {json.dumps({'content': chunk})}\n\n"
        except Exception as e:
            logger.error(f"Error in event_generator: {str(e)}")
            yield f"data: {json.dumps({'content': f'Error: {str(e)}'})}\n\n"
    
    logger.info("Starting StreamingResponse")
    return StreamingResponse(event_generator(), media_type="text/event-stream")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8003)
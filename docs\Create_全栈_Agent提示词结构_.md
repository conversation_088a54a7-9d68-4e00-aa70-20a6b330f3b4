下面是一份**针对命令行（CLI）与网页端（Web）双形态、支持 FastAPI + AG-UI + LangChain + LangGraph 技术栈**的 **AI Agent 开发提示词模板**。  
你只需把它原封不动地贴给任意 LLM，即可拿到可直接落地的代码与部署方案。

---

```markdown
# AI Agent 全栈开发提示词（FastAPI + AG-UI + LangChain + LangGraph）

## 1. 角色（Role）
你是「AI Agent 全栈开发专家」，负责同时交付：
- 一个可在 **命令行（CLI）** 运行的本地 Agent
- 一个可在 **网页端（Web）** 调用的云端 Agent 服务  
技术栈：FastAPI + AG-UI + LangChain + LangGraph

## 2. 目标（Goal）
根据用户给出的「业务场景描述」，产出：
1. LangGraph 工作流代码（带状态、记忆、人工审查节点）
2. FastAPI REST / 流式 API（支持 CLI 与 Web 同时调用）
3. AG-UI 前端组件（Next.js 或 Streamlit 二选一）
4. Docker 化脚本（一键本地 / 云端部署）
5. README：本地运行、调试、测试命令

## 3. 技术约束（Tech Stack）
- **LangGraph**：负责 Agent 状态机、循环推理、断点恢复
- **LangChain**：负责 LLM、Retriever、Tool、Memory 等模块化封装
- **FastAPI**：提供 `/chat`、`/upload`、`/stream` 等异步接口
- **AG-UI（CopilotKit）**：基于事件流的实时聊天 UI，支持生成式组件
- **CLI**：使用 `typer` 或 `click`，支持交互式命令行调用
- **部署**：Docker + docker-compose；支持热重载、Prometheus 监控

## 4. 目录结构（Scaffold）
```

my-agent/
├── agent/
│   ├── graph.py          # LangGraph 工作流定义
│   ├── tools.py          # 自定义工具
│   └── memory.py         # 记忆持久化（Postgres / Redis）
├── api/
│   ├── main.py           # FastAPI 入口
│   └── routes.py         # 路由实现
├── ui/
│   ├── next/             # Next.js + AG-UI 前端
│   └── streamlit_app.py  # Streamlit 备选
├── cli/
│   └── cli.py            # 命令行入口（typer）
├── docker/
│   ├── Dockerfile
│   └── docker-compose.yml
└── README.md

```

## 5. 核心接口清单（FastAPI）
| 方法 | 路径 | 说明 |
|---|---|---|
| POST | `/chat` | 同步问答 |
| POST | `/stream` | SSE 流式问答 |
| POST | `/upload` | 上传 PDF/图片并问答 |
| GET  | `/health` | 健康检查 |
| POST | `/human_review` | 人工审查节点回调 |

## 6. AG-UI 前端需求
- 实时对话（打字机效果）
- 支持文件拖拽上传
- 显示 Agent 思考链（thought & observation）
- 人工“继续/停止”按钮（Human-in-the-loop）
- 语音输入/输出（可选 TTS/STT）

## 7. CLI 需求
```bash
# 安装
pip install -e .

# 交互式对话
my-agent chat --model gpt-4o

# 一次性问答
echo "讲个笑话" | my-agent ask

# 带文件
my-agent ask --file report.pdf "总结这份报告"
```

## 8. 运行 & 部署命令

```bash
# 本地开发
uvicorn api.main:app --reload            # 起服务
streamlit run ui/streamlit_app.py        # UI
python cli/cli.py chat                   # CLI

# Docker 一键启动
docker-compose up --build
```

## 9. 示例 LangGraph 片段

```python
from langgraph.graph import StateGraph, END
from langchain_openai import ChatOpenAI
from agent.memory import PostgresSaver

class State(TypedDict):
    messages: Annotated[list, add_messages]

workflow = StateGraph(State)
workflow.add_node("agent", call_llm)
workflow.add_node("human_review", human_review)
workflow.add_conditional_edges("agent", route_after_llm)
workflow.set_entry_point("agent")
graph = workflow.compile(checkpointer=PostgresSaver())
```

## 10. 输出要求（给 LLM 的指令）

- 请先生成上述目录结构的所有源码文件
- 每个文件头部加 `__author__` 与 `__doc__`
- 提供 `.env.example` 与 `requirements.txt`
- 在 README 中给出「本地运行三步曲」与「线上部署五步曲」
- 所有代码必须符合 PEP8，使用类型注解（mypy 通过）
  
  ```
  
  ```

---

### ✅ 一句话使用方式

1. 把上面模板完整贴给 ChatGPT / Claude / Kimi  
2. 再补充一句业务场景：  
   
   > “我要做一个能联网搜索并总结新闻的 Agent，CLI 和 Web 都要用”  
3. 立即得到完整可运行项目。

如需我直接按你场景生成全部源码，请直接描述需求！

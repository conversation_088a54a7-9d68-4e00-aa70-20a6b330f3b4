<!DOCTYPE html>
<html>
<head>
    <title>不止AI Agent</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>不止AI Agent</h1>
    <div id="chat-box"></div>
    <input type="text" id="user-input" placeholder="请输入您的问题">
    <button id="send-btn">发送</button>

    <script>
        $(document).ready(function() {
            $('#send-btn').click(function() {
                var userMessage = $('#user-input').val();
                if (userMessage.trim() !== '') {
                    $('#chat-box').append('<p><strong>您:</strong> ' + userMessage + '</p>');
                    $('#user-input').val('');

                    $.ajax({
                        url: '/chat',
                        method: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({message: userMessage}),
                        success: function(response) {
                            $('#chat-box').append('<p><strong>Agent:</strong> ' + response.response + '</p>');
                        }
                    });
                }
            });
        });
    </script>
</body>
</html>
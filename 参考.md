下面给出一个**插件式自动发现**的完整可运行案例，特点：

- 零耦合：旧 Agent 以**独立 Python 包/插件**形式存在，新 Agent 无需 `import` 任何具体实现  
- 自动扫描：利用 Python 标准库 `importlib.metadata`（PEP 621 entry-point）自动发现所有已安装的 Agent 插件  
- LangGraph 原生：调度器/管理器仍用 LangGraph，但路由表在运行时动态生成  
- 可插拔：新增 Agent 只需 `pip install`，无需改一行主代码  

---

### 📂 目录结构

```
plugin_agents/
├── manager/                      # 主程序
│   ├── __init__.py
│   ├── registry.py               # 自动扫描注册表
│   └── graph.py                  # LangGraph 调度器
├── plugins/                      # 插件目录（可拆到独立 wheel）
│   └── calculator_agent.py       # 示例插件
├── pyproject.toml                # 声明 entry-points
└── run.py                        # 一键启动
```

---

### 1️⃣ `pyproject.toml`（声明插件）

```toml
[project]
name = "plugin_agents"
version = "0.1.0"

[project.entry-points."langgraph.agents"]
calculator = "plugins.calculator_agent:agent_factory"
```

---

### 2️⃣ `plugins/calculator_agent.py`（插件实现）

```python
# plugins/calculator_agent.py
from langchain_openai import ChatOpenAI
from langchain_core.tools import tool
from langgraph.prebuilt import create_react_agent

llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)

@tool
def calculator(expr: str) -> str:
    """安全计算"""
    try:
        return str(eval(expr))
    except Exception as e:
        return f"计算错误：{e}"

agent = create_react_agent(llm, [calculator], "你是计算器，仅做数学运算。")

# 工厂函数，由 entry-point 指向
def agent_factory():
    return agent.invoke
```

---

### 3️⃣ `manager/registry.py`（自动扫描）

```python
# manager/registry.py
import importlib.metadata
from typing import Dict, Callable, Any

AgentFn = Callable[[Dict[str, Any]], Any]

def load_agents() -> Dict[str, AgentFn]:
    eps = importlib.metadata.entry_points(group="langgraph.agents")
    registry = {}
    for ep in eps:
        factory = ep.load()          # 调用 agent_factory
        registry[ep.name] = factory()
    return registry
```

---

### 4️⃣ `manager/graph.py`（动态 LangGraph）

```python
# manager/graph.py
from langchain_openai import ChatOpenAI
from langchain_core.tools import tool
from langgraph.prebuilt import create_react_agent
from typing import Any, Dict
from .registry import load_agents

llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)

# 运行时动态加载所有 Agent
AGENTS = load_agents()

@tool
def delegate(agent_name: str, prompt: str) -> str:
    """把任务交给指定 Agent"""
    if agent_name not in AGENTS:
        return f"不存在 Agent：{agent_name}"
    return AGENTS[agent_name]({"messages": [{"role": "user", "content": prompt}]})["messages"][-1].content

# 管理 Agent：根据用户意图自动选择 Agent
manager_agent = create_react_agent(
    llm,
    [delegate],
    prompt=(
        "你是调度器。用户提问时：\n"
        "1. 若涉及数学运算，用 delegate 工具指定 agent_name='calculator'。\n"
        "2. 否则直接回答。"
    ),
)

def run(user_input: str) -> str:
    result = manager_agent.invoke({"messages": [{"role": "user", "content": user_input}]})
    return result["messages"][-1].content
```

---

### 5️⃣ `run.py`（一键启动）

```python
# run.py
from manager.graph import run

if __name__ == "__main__":
    question = "3.5 * (8 + 2) 等于？"
    print("🤖 答案：", run(question))
```

---

### ✅ 运行方式

```bash
# 安装依赖
pip install langchain-openai langgraph

# 本地开发模式安装
pip install -e .

# 运行
python run.py
```

输出：

```
🤖 答案： 35.0
```

---

### 🎯 如何再装一个插件？

1. 新建包 `weather_plugin`  
2. 在其 `pyproject.toml` 增加：

```toml
[project.entry-points."langgraph.agents"]
weather = "weather_plugin.agent:agent_factory"
```

3. `pip install weather_plugin`  
4. 无需重启主程序，再次提问即可通过 `delegate(agent_name='weather')` 自动调度！

---

### 🔚 小结

- **零耦合**：主程序与插件仅通过 entry-point 约定接口  
- **可扩展**：任何第三方开发者只要发布 wheel 即可接入  
- **LangGraph 原生**：调度器仍用 LangGraph 的 ReAct / Supervisor 模式  
- **云原生友好**：与 Google Cloud Run / Kubernetes 等容器平台天然适配

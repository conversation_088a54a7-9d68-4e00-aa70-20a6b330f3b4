# registry.py
from typing import Dict, Callable, Any

class _AgentRegistry:
    def __init__(self):
        self._agents: Dict[str, Callable[[Dict[str, Any]], Any]] = {}

    def register(self, name: str, agent_fn: Callable[[Dict[str, Any]], Any]):
        self._agents[name] = agent_fn

    def get(self, name: str) -> Callable[[Dict[str, Any]], Any]:
        if name not in self._agents:
            raise KeyError(f"Agent '{name}' 未注册")        
        return self._agents[name]

    def list_agents(self):
        return list(self._agents.keys())

# 注册 prompt_assistant
from agents.prompt_assistant.prompt_assistant import process_user_input as prompt_assistant_process

registry = _AgentRegistry()   # 单例
registry.register("prompt_assistant", prompt_assistant_process)